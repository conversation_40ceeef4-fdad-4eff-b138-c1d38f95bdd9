import { Request, Response } from 'express';
import { InterviewConfig, IInterviewConfig } from '../models/InterviewConfig';
import { v4 as uuidv4 } from 'uuid';
import { AuthenticatedRequest } from '../middleware/auth';

interface SaveInterviewRequest {
  user: {
    id?: string;
    email?: string;
    prompt?: string;
  };
  projectData: {
    technical: {
      [sectionName: string]: Array<{ [questionName: string]: string }>;
    };
    functional: {
      [sectionName: string]: Array<{ [questionName: string]: string }>;
    };
  };
}

interface UpdateInterviewRequest extends SaveInterviewRequest {
  uuid: string;
}

// Helper function to convert old format to new format
const convertToNewFormat = (
  functionalAnswers: { [key: string]: string },
  technicalAnswers: { [key: string]: string },
  functionalSections: Array<{id: number, title: string, questions: string[]}>,
  technicalSections: Array<{id: number, title: string, questions: string[]}>
) => {
  const projectData = {
    functional: {} as { [sectionName: string]: Array<{ [questionName: string]: string }> },
    technical: {} as { [sectionName: string]: Array<{ [questionName: string]: string }> }
  };

  // Process functional answers
  functionalSections.forEach(section => {
    const sectionName = section.title;
    const sectionAnswers: Array<{ [questionName: string]: string }> = [];
    
    section.questions.forEach(question => {
      if (functionalAnswers[question]) {
        sectionAnswers.push({
          [question]: functionalAnswers[question]
        });
      }
    });
    
    if (sectionAnswers.length > 0) {
      projectData.functional[sectionName] = sectionAnswers;
    }
  });

  // Process technical answers  
  technicalSections.forEach(section => {
    const sectionName = section.title;
    const sectionAnswers: Array<{ [questionName: string]: string }> = [];
    
    section.questions.forEach(question => {
      if (technicalAnswers[question]) {
        sectionAnswers.push({
          [question]: technicalAnswers[question]
        });
      }
    });
    
    if (sectionAnswers.length > 0) {
      projectData.technical[sectionName] = sectionAnswers;
    }
  });

  return projectData;
};

// Utility: derive a default name from the first 20 words of the prompt
const deriveNameFromPrompt = (prompt?: string): string => {
  if (!prompt || !prompt.trim()) return 'Untitled Project';
  const words = prompt.trim().split(/\s+/);
  const first20 = words.slice(0, 20).join(' ');
  return first20;
};

// Save a new interview configuration
export const saveInterviewConfig = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { 
      user,
      projectData,
      uuid: existingUuid
    }: SaveInterviewRequest & { uuid?: string } = req.body;

    // Validate required fields
    if (!projectData || !projectData.functional || !projectData.technical) {
      return res.status(400).json({
        success: false,
        message: 'Project data must include both functional and technical sections'
      });
    }

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'User information is required'
      });
    }

    let savedConfig;

    // Prepare data object
    const dataToSave = {
      user,
      projectData,
      updatedAt: new Date()
    } as Partial<IInterviewConfig>;

    // Check if this is an update to existing configuration
    if (existingUuid) {
      savedConfig = await InterviewConfig.findOneAndUpdate(
        { uuid: existingUuid },
        dataToSave,
        { new: true, select: '-_id -__v' }
      );

      if (!savedConfig) {
        return res.status(404).json({
          success: false,
          message: 'Interview configuration not found for update'
        });
      }
    } else {
      // Create new interview config with default name derived from prompt
      const interviewConfig = new InterviewConfig({
        uuid: uuidv4(),
        name: deriveNameFromPrompt(user?.prompt),
        ...dataToSave
      });

      savedConfig = await interviewConfig.save();
    }

    res.status(201).json({
      success: true,
      message: 'Interview configuration saved successfully',
      data: {
        uuid: savedConfig.uuid,
        name: savedConfig.name,
        user: savedConfig.user,
        projectData: savedConfig.projectData,
        createdAt: savedConfig.createdAt,
        updatedAt: savedConfig.updatedAt
      }
    });
  } catch (error: any) {
    console.error('Error saving interview config:', error);
    
    // Handle duplicate UUID error (though extremely unlikely)
    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: 'UUID collision occurred, please try again'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while saving interview configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get interview configuration by UUID
export const getInterviewConfig = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { uuid } = req.params;

    if (!uuid) {
      return res.status(400).json({
        success: false,
        message: 'UUID is required'
      });
    }

    const interviewConfig = await InterviewConfig.findOne({ uuid }).select('-_id -__v');

    if (!interviewConfig) {
      return res.status(404).json({
        success: false,
        message: 'Interview configuration not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        uuid: interviewConfig.uuid,
        name: interviewConfig.name,
        user: interviewConfig.user,
        projectData: interviewConfig.projectData,
        createdAt: interviewConfig.createdAt,
        updatedAt: interviewConfig.updatedAt
      }
    });
  } catch (error: any) {
    console.error('Error fetching interview config:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching interview configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all interview configurations for a user (optional)
export const getUserInterviewConfigs = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    // Query by user.id field in the new schema
    const configs = await InterviewConfig.find({ 'user.id': userId })
      .select('-_id -__v')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await InterviewConfig.countDocuments({ 'user.id': userId });

    res.status(200).json({
      success: true,
      data: {
        configs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching user interview configs:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching user interview configurations',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get simplified history for a user (new endpoint for quick history access)
export const getUserHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const limit = parseInt(req.query.limit as string) || 20;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    // Get recent configs with minimal data for history list
    const configs = await InterviewConfig.find({ 'user.id': userId })
      .select('uuid name user.prompt createdAt updatedAt')
      .sort({ updatedAt: -1 })
      .limit(limit);

    res.status(200).json({
      success: true,
      data: configs.map(config => ({
        uuid: config.uuid,
        name: config.name || deriveNameFromPrompt(config.user?.prompt),
        prompt: config.user?.prompt || 'Untitled Project',
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
      }))
    });
  } catch (error: any) {
    console.error('Error fetching user history:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching user history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Rename interview title (updates name only, keeps prompt unchanged)
export const renameInterviewTitle = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { uuid } = req.params;
    const { title } = req.body as { title?: string };

    if (!uuid || !title || !title.trim()) {
      return res.status(400).json({ success: false, message: 'uuid and non-empty title are required' });
    }

    const updated = await InterviewConfig.findOneAndUpdate(
      { uuid },
      { $set: { name: title.trim(), updatedAt: new Date() } },
      { new: true }
    ).select('uuid name user.prompt createdAt updatedAt');

    if (!updated) {
      return res.status(404).json({ success: false, message: 'Interview not found' });
    }

    return res.json({
      success: true,
      message: 'Title updated',
      data: {
        uuid: updated.uuid,
        name: updated.name || deriveNameFromPrompt(updated.user?.prompt),
        prompt: updated.user?.prompt || 'Untitled Project',
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt
      }
    });
  } catch (error: any) {
    console.error('Error renaming interview title:', error);
    return res.status(500).json({ success: false, message: 'Failed to rename interview title' });
  }
};

// Delete interview and related data
export const deleteInterview = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { uuid } = req.params;
    if (!uuid) {
      return res.status(400).json({ success: false, message: 'uuid is required' });
    }

    // Delete InterviewConfig
    const deleted = await InterviewConfig.findOneAndDelete({ uuid });
    if (!deleted) {
      return res.status(404).json({ success: false, message: 'Interview not found' });
    }

    // Cascade delete related data
    const { ConversationMessage } = await import('../models/ConversationHistory');
    const { BuildResult } = await import('../models/BuildResult');

    const [convResult, buildResult] = await Promise.all([
      ConversationMessage.deleteMany({ interviewUuid: uuid }),
      BuildResult.deleteMany({ interviewUuid: uuid })
    ]);

    return res.json({
      success: true,
      message: 'Interview deleted',
      data: {
        deletedUuid: uuid,
        conversationsDeleted: convResult.deletedCount || 0,
        buildResultsDeleted: buildResult.deletedCount || 0
      }
    });
  } catch (error: any) {
    console.error('Error deleting interview:', error);
    return res.status(500).json({ success: false, message: 'Failed to delete interview' });
  }
}; 