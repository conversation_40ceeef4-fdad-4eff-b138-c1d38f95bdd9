import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>u, Dropdown, <PERSON><PERSON>, Modal, Spin } from 'antd';
import { MoreOutlined, EditOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { interviewAPI } from '../utils/api';
import { useInfiniteScroll } from '../hooks/useInfiniteScroll';
import arrowIcon from '../assets/images/arrowIcon.svg';

interface RecentProject {
  uuid: string;
  name?: string;
  prompt: string;
  createdAt: string;
  updatedAt: string;
}

interface RecentProjectsProps {
  userId: string;
  onProjectClick: (uuid: string) => void;
  onProjectsUpdate?: () => void;
}

const RecentProjects: React.FC<RecentProjectsProps> = ({ userId, onProjectClick, onProjectsUpdate }) => {
  const [editingProject, setEditingProject] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<RecentProject | null>(null);
  const [tooltipData, setTooltipData] = useState<{text: string, x: number, y: number} | null>(null);
  const clickTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressRef = useRef(false);

  // Infinite scroll hook
  const {
    data: projects,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    scrollRef
  } = useInfiniteScroll<RecentProject>({
    fetchData: async (page, limit) => {
      const response = await interviewAPI.getUserHistory(userId, { page, limit });
      return {
        data: response.data,
        pagination: response.pagination
      };
    },
    limit: 18
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInHours < 168) {
      const days = Math.floor(diffInHours / 24);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleRename = async () => {
    if (!editName.trim() || !editingProject) return;
    
    try {
      await interviewAPI.renameHistory(editingProject, editName.trim());
      setEditingProject(null);
      setEditName('');
      // Refresh the projects list
      handleProjectUpdate();
    } catch (error) {
      console.error('Failed to rename project:', error);
    }
  };

  const handleDelete = async () => {
    if (!projectToDelete) return;
    
    try {
      await interviewAPI.deleteHistory(projectToDelete.uuid);
      setProjectToDelete(null);
      setIsDeleteModalOpen(false);
      // Refresh the projects list
      handleProjectUpdate();
    } catch (error) {
      console.error('Failed to delete project:', error);
    }
  };

  const handleCardClick = (uuid: string, event: React.MouseEvent) => {
    // Don't navigate if dropdown is open or if it's a long press
    if (isDropdownOpen === uuid || isLongPressRef.current) {
      return;
    }
    onProjectClick(uuid);
  };

  const handleCardMouseDown = (uuid: string) => {
    isLongPressRef.current = false;
    clickTimerRef.current = setTimeout(() => {
      isLongPressRef.current = true;
    }, 200); // 200ms threshold for long press
  };

  const handleCardMouseUp = () => {
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current);
      clickTimerRef.current = null;
    }
  };

  const handleDropdownClick = (uuid: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setIsDropdownOpen(isDropdownOpen === uuid ? null : uuid);
  };

  const openRenameModal = (project: RecentProject) => {
    setEditingProject(project.uuid);
    setEditName(project.name || project.prompt);
    setIsDropdownOpen(null);
  };

  const openDeleteModal = (project: RecentProject) => {
    setProjectToDelete(project);
    setIsDeleteModalOpen(true);
    setIsDropdownOpen(null);
  };

  const getMenuItems = (project: RecentProject) => [
    {
      key: 'rename',
      icon: <EditOutlined />,
      label: 'Rename',
      onClick: () => openRenameModal(project),
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: 'Delete',
      danger: true,
      onClick: () => openDeleteModal(project),
    },
  ];

  // Handle project updates
  const handleProjectUpdate = () => {
    refresh();
    if (onProjectsUpdate) {
      onProjectsUpdate();
    }
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-[25px] p-4">
        <h3 className="text-white text-lg font-semibold mb-2">Recent Projects</h3>
        <div className="flex justify-center items-center py-8">
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 24, color: 'white' }} spin />}
            size="large"
          />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-[25px] p-4">
        <h3 className="text-white text-lg font-semibold mb-2">Recent Projects</h3>
        <div className="text-white/70 text-center py-4">
          Error loading projects: {error}
        </div>
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-[25px] p-4">
        <h3 className="text-white text-lg font-semibold mb-2">Recent Projects</h3>
        <div className="text-white/70 text-center py-4">
          No projects found. Start creating your first project!
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white/10 backdrop-blur-sm rounded-[25px] p-4">
        <h3 className="text-white text-lg font-semibold mb-2">Recent Projects</h3>

        {/* Scrollable container with fixed height for 2 rows */}
        <div
          ref={scrollRef}
          className="max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent"
          style={{ scrollbarWidth: 'thin' }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pr-2">
            {projects.map((project) => (
              <motion.div
                key={project.uuid}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-white/20 backdrop-blur-sm rounded-[15px] p-4 cursor-pointer hover:bg-white/30 transition-all duration-200 relative"
                onClick={(e) => handleCardClick(project.uuid, e)}
                onMouseDown={() => handleCardMouseDown(project.uuid)}
                onMouseUp={handleCardMouseUp}
                onMouseLeave={handleCardMouseUp}
              >
              <div className="flex justify-between items-start mb-2">
                <div className="relative flex-1 mr-2 min-w-0">
                  <h4 
                    className="text-white font-medium text-sm truncate w-full cursor-pointer"
                    onMouseEnter={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      setTooltipData({
                        text: project.name || project.prompt,
                        x: rect.left + rect.width / 2,
                        y: rect.top - 10
                      });
                    }}
                    onMouseLeave={() => setTooltipData(null)}
                  >
                    {project.name || project.prompt}
                  </h4>
                </div>
                <Dropdown
                  menu={{ items: getMenuItems(project) }}
                  trigger={['click']}
                  placement="bottomRight"
                  open={isDropdownOpen === project.uuid}
                  onOpenChange={(open) => setIsDropdownOpen(open ? project.uuid : null)}
                >
                  <Button
                    type="text"
                    icon={<MoreOutlined style={{ color: 'white' }} />}
                    className="!text-white hover:!bg-gray-500 hover:!text-white !border-none !shadow-none"
                    style={{ 
                      color: 'white',
                      backgroundColor: 'transparent',
                      border: 'none',
                      boxShadow: 'none'
                    }}
                    onClick={(e) => handleDropdownClick(project.uuid, e)}
                  />
                </Dropdown>
              </div>
              <div className="text-white/50 text-xs">
                Updated {formatDate(project.updatedAt)}
              </div>
            </motion.div>
          ))}
          </div>

          {/* Loading indicator for more data */}
          {loadingMore && (
            <div className="flex justify-center items-center py-4">
              <Spin
                indicator={<LoadingOutlined style={{ fontSize: 16, color: 'white' }} spin />}
                size="small"
              />
              <span className="text-white/70 ml-2 text-sm">Loading more projects...</span>
            </div>
          )}

          {/* View More button */}
          {hasMore && !loadingMore && (
            <div className="flex justify-center py-4">
              <Button
                type="text"
                onClick={loadMore}
                className="!text-white hover:!bg-white/20 !border-white/30 hover:!border-white/50 transition-all duration-200"
                style={{
                  color: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  backgroundColor: 'transparent'
                }}
              >
                View More Projects
              </Button>
            </div>
          )}

          {/* End of data message */}
          {!hasMore && projects.length > 0 && (
            <div className="text-center py-4">
              <span className="text-white/50 text-sm">
                You've reached the end of your project history
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Global Tooltip */}
      {tooltipData && (
        <div 
          className="fixed px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg pointer-events-none whitespace-nowrap z-[9999]"
          style={{
            left: tooltipData.x,
            top: tooltipData.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          {tooltipData.text}
        </div>
      )}

      {/* Rename Modal - Same as HistoryDrawer */}
      <Modal
        title={`Update Conversation - ${
          editingProject ? (projects.find(p => p.uuid === editingProject)?.name || projects.find(p => p.uuid === editingProject)?.prompt) : ""
        }`}
        open={!!editingProject}
        footer={null}
        onCancel={() => setEditingProject(null)}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto" },
        }}
      >
        <div className="m-5" onClick={(e) => e.stopPropagation()}>
          <div className="relative">
            <input
              type="text"
              className="w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ps-7 pe-14 outline-none"
              onChange={(e) => setEditName(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleRename()}
              placeholder="conversation"
              value={editName}
            />
            <button
              className="bg-[#1E1E1E] py-4.5 px-6 rounded-[25px] absolute top-0 -right-2 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
              onClick={handleRename}
              disabled={!editName.length}
            >
              <img src={arrowIcon} alt="icon" height={12} width={14} />
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="Delete Project"
        open={isDeleteModalOpen}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setProjectToDelete(null);
        }}
        onOk={handleDelete}
        okText="Delete"
        cancelText="Cancel"
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to delete "{projectToDelete?.name || projectToDelete?.prompt}"?</p>
        <p className="text-gray-500 text-sm mt-2">This action cannot be undone.</p>
      </Modal>
    </>
  );
};

export default RecentProjects; 