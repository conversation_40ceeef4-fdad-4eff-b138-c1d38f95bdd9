import { renderHook, act } from '@testing-library/react';
import { useInfiniteScroll } from '../useInfiniteScroll';

// Mock data for testing
const mockData = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  name: `Item ${i + 1}`,
  description: `Description for item ${i + 1}`
}));

// Mock fetch function
const createMockFetchData = (totalItems: number = 50) => {
  return async (page: number, limit: number) => {
    const startIndex = (page - 1) * limit;
    const endIndex = Math.min(startIndex + limit, totalItems);
    const data = mockData.slice(startIndex, endIndex);
    
    return {
      data,
      pagination: {
        page,
        limit,
        total: totalItems,
        hasMore: endIndex < totalItems,
        totalPages: Math.ceil(totalItems / limit)
      }
    };
  };
};

describe('useInfiniteScroll', () => {
  it('should fetch initial data on mount', async () => {
    const mockFetchData = createMockFetchData(50);
    
    const { result } = renderHook(() =>
      useInfiniteScroll({
        fetchData: mockFetchData,
        limit: 18
      })
    );

    // Initially loading should be true
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toEqual([]);

    // Wait for initial data to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.data).toHaveLength(18);
    expect(result.current.hasMore).toBe(true);
  });

  it('should load more data when loadMore is called', async () => {
    const mockFetchData = createMockFetchData(50);
    
    const { result } = renderHook(() =>
      useInfiniteScroll({
        fetchData: mockFetchData,
        limit: 18
      })
    );

    // Wait for initial data
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(18);

    // Load more data
    await act(async () => {
      result.current.loadMore();
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(36);
    expect(result.current.hasMore).toBe(true);
  });

  it('should handle end of data correctly', async () => {
    const mockFetchData = createMockFetchData(20); // Only 20 items total
    
    const { result } = renderHook(() =>
      useInfiniteScroll({
        fetchData: mockFetchData,
        limit: 18
      })
    );

    // Wait for initial data
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(18);
    expect(result.current.hasMore).toBe(true);

    // Load more data (should get remaining 2 items)
    await act(async () => {
      result.current.loadMore();
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(20);
    expect(result.current.hasMore).toBe(false);
  });

  it('should refresh data correctly', async () => {
    const mockFetchData = createMockFetchData(50);
    
    const { result } = renderHook(() =>
      useInfiniteScroll({
        fetchData: mockFetchData,
        limit: 18
      })
    );

    // Wait for initial data and load more
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      result.current.loadMore();
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(36);

    // Refresh should reset to first page
    await act(async () => {
      result.current.refresh();
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data).toHaveLength(18);
    expect(result.current.hasMore).toBe(true);
  });

  it('should handle errors correctly', async () => {
    const mockFetchData = jest.fn().mockRejectedValue(new Error('Network error'));
    
    const { result } = renderHook(() =>
      useInfiniteScroll({
        fetchData: mockFetchData,
        limit: 18
      })
    );

    // Wait for error to be set
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe('Network error');
    expect(result.current.data).toEqual([]);
    expect(result.current.hasMore).toBe(false);
  });
});
