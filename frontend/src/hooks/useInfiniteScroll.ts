import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  fetchData: (page: number, limit: number) => Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }>;
  limit?: number;
  initialPage?: number;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMore: () => void; // Manual load more only
  refresh: () => void;
  scrollRef: React.RefObject<HTMLDivElement>;
}

export function useInfiniteScroll<T>({
  fetchData,
  limit = 18,
  initialPage = 1
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [initialized, setInitialized] = useState(false);

  const scrollRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);
  const fetchDataRef = useRef(fetchData);

  // Update fetchData ref when it changes
  useEffect(() => {
    fetchDataRef.current = fetchData;
  }, [fetchData]);

  // Initial data fetch
  const fetchInitialData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      isLoadingRef.current = true;

      const response = await fetchDataRef.current(initialPage, limit);
      setData(response.data);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(initialPage);
      setInitialized(true);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch data');
      setData([]);
      setHasMore(false);
      setInitialized(true);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [limit, initialPage]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingRef.current) return;

    try {
      setLoadingMore(true);
      setError(null);
      isLoadingRef.current = true;

      const nextPage = currentPage + 1;
      const response = await fetchDataRef.current(nextPage, limit);

      setData(prevData => [...prevData, ...response.data]);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(nextPage);
    } catch (err: any) {
      setError(err.message || 'Failed to load more data');
    } finally {
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [limit, currentPage, hasMore]);

  // Refresh data (reset to first page)
  const refresh = useCallback(() => {
    setCurrentPage(initialPage);
    fetchInitialData();
  }, [fetchInitialData, initialPage]);

  // Scroll event handler for infinite scroll - DISABLED for manual loading only
  const handleScroll = useCallback((e: Event) => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    // Prevent page scrolling when reaching container boundaries
    e.stopPropagation();

    const { scrollTop, scrollHeight, clientHeight } = scrollElement;

    // Prevent page scroll when at top or bottom of container
    if (scrollTop === 0 && e instanceof WheelEvent && e.deltaY < 0) {
      e.preventDefault();
    } else if (scrollTop + clientHeight >= scrollHeight && e instanceof WheelEvent && e.deltaY > 0) {
      e.preventDefault();
    }

    // DISABLED: Automatic infinite scroll - only manual "View More" button
    // const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
    // if (scrollPercentage >= 0.8 && hasMore && !isLoadingRef.current) {
    //   loadMore();
    // }
  }, []);

  // Stable scroll handler ref to prevent listener re-attachment
  const handleScrollRef = useRef(handleScroll);
  useEffect(() => {
    handleScrollRef.current = handleScroll;
  }, [handleScroll]);

  // Set up scroll and wheel listeners to prevent page scrolling
  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    const scrollHandler = (e: Event) => handleScrollRef.current(e);
    const wheelHandler = (e: WheelEvent) => handleScrollRef.current(e);

    scrollElement.addEventListener('scroll', scrollHandler, { passive: false });
    scrollElement.addEventListener('wheel', wheelHandler, { passive: false });

    return () => {
      scrollElement.removeEventListener('scroll', scrollHandler);
      scrollElement.removeEventListener('wheel', wheelHandler);
    };
  }, []); // Empty dependency array since we use ref

  // Initial data fetch on mount - only run once
  useEffect(() => {
    if (!initialized) {
      fetchInitialData();
    }
  }, [fetchInitialData, initialized]);

  return {
    data,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    scrollRef
  };
}
