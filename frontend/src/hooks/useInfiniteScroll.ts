import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  fetchData: (page: number, limit: number) => Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }>;
  limit?: number;
  initialPage?: number;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMore: () => void;
  refresh: () => void;
  scrollRef: React.RefObject<HTMLDivElement>;
}

export function useInfiniteScroll<T>({
  fetchData,
  limit = 18,
  initialPage = 1
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  
  const scrollRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);

  // Initial data fetch
  const fetchInitialData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      isLoadingRef.current = true;
      
      const response = await fetchData(initialPage, limit);
      setData(response.data);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(initialPage);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch data');
      setData([]);
      setHasMore(false);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [fetchData, limit, initialPage]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingRef.current) return;

    try {
      setLoadingMore(true);
      setError(null);
      isLoadingRef.current = true;
      
      const nextPage = currentPage + 1;
      const response = await fetchData(nextPage, limit);
      
      setData(prevData => [...prevData, ...response.data]);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(nextPage);
    } catch (err: any) {
      setError(err.message || 'Failed to load more data');
    } finally {
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [fetchData, limit, currentPage, hasMore]);

  // Refresh data (reset to first page)
  const refresh = useCallback(() => {
    setCurrentPage(initialPage);
    fetchInitialData();
  }, [fetchInitialData, initialPage]);

  // Scroll event handler for infinite scroll
  const handleScroll = useCallback(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement || !hasMore || isLoadingRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
    
    // Trigger load more when user scrolls to 80% of the content
    if (scrollPercentage >= 0.8) {
      loadMore();
    }
  }, [hasMore, loadMore]);

  // Set up scroll listener
  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // Initial data fetch on mount
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  return {
    data,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    scrollRef
  };
}
