@import "antd/dist/reset.css";
@import "tailwindcss";

/* Fonts */
@font-face {
  font-family: 'Inter';
  src: url('./assets/fonts/Inter/Inter-VariableFont_opsz,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: 'Charter';
  src: url('./assets/fonts/charter/ttf/Charter Regular.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: 'Tw-cen-mt';
  src: url('./assets/fonts/Tw-Cen-Mt/Tw-Cen-MT-Condensed-Font.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
}

/* Match demo font usage */
.listCardContainer,
.bg-drawer,
.report-section,
.home-section,
.ant-flex,
.ant-form-item-control-input-content,
.font-Inter {
  font-family: 'Inter' !important;
}

.footer-Section,
.todo-container {
  font-family: 'Charter';
}

/* Backgrounds and sizing */
.cardContainer { height: calc(100vh - 400px); }
.listCardContainer { height: calc(100vh - 146px); }
.bg-drawer {
  background-image: url('./assets/images/historyDrawerBg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overscroll-behavior: contain;
}
.historyList { 
  height: calc(100vh - 270px); 
  overscroll-behavior: contain;
}

/* Scrollbars */
.scrollbar-hide::-webkit-scrollbar { display: none; }
.scrollbar-custom::-webkit-scrollbar { width: 6px; height: 6px; }
.scrollbar-custom::-webkit-scrollbar-thumb { background-color: rgba(0, 0, 0, 0.3); border-radius: 10px; }

/* Utilities */
.listViewContainer { width: calc(100% - 279px); }
.truncate-text { display: -webkit-box; -webkit-line-clamp: 8; line-clamp: 8; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; word-break: break-word; }
.truncate-title { display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; word-break: break-word; }
.truncate-item { display: -webkit-box; -webkit-line-clamp: 1; line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; word-break: break-word; }
.bg-todolist { background-image: url('./assets/images/TodoBackground.png'); background-position: center; background-repeat: no-repeat; }
.textarea-hide-scrollbar { overflow: auto; scrollbar-width: none; -ms-overflow-style: none; }
.textarea-hide-scrollbar::-webkit-scrollbar { display: none; }

@media print { .no-print { display: none !important; } }
@page { margin: 2cm; }
@media (max-width: 640px) { .cardConatiner { height: calc(100vh - 350px); } }
@media (max-width: 1023px) { .listViewContainer { width: 100%; } }

/* Base defaults */
body {
  margin: 0;
  background: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code { font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace; }

/* Ensure popover action buttons show white text in drawer menu */
.ant-popover .font-Inter button {
  color: #ffffff !important;
}

/* Auth input enforced colors */
.auth-input {
  color: #ffffff !important;
  font-family: 'Inter', sans-serif !important;
  font-weight: 900 !important;
}
.auth-input::placeholder {
  color: #cfcfcf !important;
}
/* Handle WebKit autofill overriding colors */
.auth-input:-webkit-autofill,
.auth-input:-webkit-autofill:hover,
.auth-input:-webkit-autofill:focus,
.auth-input:-webkit-autofill:active {
  -webkit-text-fill-color: #ffffff !important;
  transition: background-color 600000s 0s, color 600000s 0s !important;
}

/* Placeholder text styling to match the image */
input::placeholder,
textarea::placeholder {
  color: #6B7280 !important; /* Medium grey color */
  font-weight: 400 !important; /* Regular font weight */
  font-family: 'Charter' !important; /* Use same Charter font as the rest of the app */
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
