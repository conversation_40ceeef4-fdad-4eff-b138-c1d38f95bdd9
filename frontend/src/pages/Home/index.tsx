import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Carousel, Modal, message } from 'antd';
import {
  LinkOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { interviewAPI } from '../../utils/api';
import SearchBar from '../../components/SearchBar';
import Heading from '../../components/Heading';
import Footer from '../../components/Footer';
import RecentProjects from '../../components/RecentProjects';
import useWindowResize from '../../hooks/useWindowResize';

// Import assets
import headingImg from '../../assets/images/headingImg.svg';
import homeBanner from '../../assets/images/homeBanner.png';
import { ShareIcon } from '../../components/icons/CustomIcons';

interface RecentProject {
  uuid: string;
  name?: string;
  prompt: string;
  createdAt: string;
  updatedAt: string;
}

const Home: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { screenWidth } = useWindowResize();
  const [inputValue, setInputValue] = useState<string>('');
  const [attachments, setAttachments] = useState<
    Array<{
      file: File | null;
      preview: string | null;
      name: string;
      type: string;
    }>
  >([]);
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [recentProjects, setRecentProjects] = useState<RecentProject[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const documentInputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchRecentProjects();
    }
  }, [user?.id]);

  const fetchRecentProjects = async () => {
    if (!user?.id) return;

    try {
      setIsLoadingProjects(true);
      const response = await interviewAPI.getUserHistory(user.id, 6);
      if (response.success) {
        setRecentProjects(response.data);
      }
    } catch (error) {
      console.error('Error fetching recent projects:', error);
    } finally {
      setIsLoadingProjects(false);
    }
  };

  const handleDocumentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    if (file) {
      setAttachments((prev) => [
        ...prev,
        {
          file,
          preview: 'doc',
          name: file.name,
          type: (file.name.split('.').pop() || '').toLowerCase(),
        },
      ]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const handleUrlSubmit = () => {
    if (!urlInput.trim()) {
      message.error('Please enter a valid URL');
      return;
    }

    try {
      new URL(urlInput);
      setAttachments((prev) => [
        ...prev,
        {
          file: null,
          preview: 'url',
          name: urlInput,
          type: 'url',
        },
      ]);
      setUrlInput('');
      setIsUrlModalOpen(false);
    } catch {
      message.error('Please enter a valid URL');
    }
  };

  const handleDocumentClick = () => {
    if (documentInputRef.current) {
      documentInputRef.current.click();
    }
  };

  const handleSearch = async () => {
    if (!inputValue.trim()) return;

    // Navigate to interview page with the user's prompt
    navigate('/interview', {
      state: {
        prompt: inputValue
      }
    });
  };

  const handlePaste = async (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const items = event.clipboardData?.items;

    if (!items) return;

    for (const item of Array.from(items)) {
      // Handle URLs and text
      if (item.type === 'text/plain') {
        const text = await new Promise<string>((resolve) => {
          item.getAsString((s) => resolve(s));
        });

        // Check if pasted content is URL
        try {
          new URL(text);
          setAttachments((prev) => [
            ...prev,
            {
              file: null,
              preview: 'url',
              name: text,
              type: 'url',
            },
          ]);
          return;
        } catch {
          // Not a URL, use as normal input
          setInputValue(text);
        }
      }

      // Handle images
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (!file) continue;

        const reader = new FileReader();
        reader.onloadend = () => {
          setAttachments((prev) => [
            ...prev,
            {
              file,
              preview: reader.result as string,
              name: file.name || 'Pasted image',
              type: 'image',
            },
          ]);
        };
        reader.readAsDataURL(file);
        return;
      }

      // Handle other files
      if (item.type.indexOf('application/') === 0) {
        const file = item.getAsFile();
        if (!file) continue;

        setAttachments((prev) => [
          ...prev,
          {
            file,
            preview: 'doc',
            name: file.name,
            type: (file.name.split('.').pop() || '').toLowerCase(),
          },
        ]);
        return;
      }
    }
  };

  const handleAudioPlay = (url: string) => {
    if (playingAudio === url) {
      audioRef.current?.pause();
      setPlayingAudio(null);
    } else {
      if (audioRef.current) {
        audioRef.current.src = url;
        audioRef.current.play();
        setPlayingAudio(url);
      }
    }
  };

  const handleOpenProject = (uuid: string) => {
    navigate(`/build/${uuid}`);
  };

  return (
    <>
      <motion.div
        initial={{ x: 0, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 0, opacity: 0 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="relative h-[100vh] w-full home-section"
      >
        {/* Background Carousel or Static Image */}
        <div 
          className="h-[100vh] w-full object-cover"
          style={{
            backgroundImage: `url(${homeBanner})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        />

        <div className="absolute top-0 left-0 w-full h-full flex flex-col">
          <Heading />
          
          <div className="flex flex-col gap-0 w-full items-center flex-1 justify-center">
            <img
              src={headingImg}
              alt="headingImg"
              loading="eager"
              className="mix-blend-screen w-[620px] h-[120px] px-12 sm:px-20 mb-2"
            />
            
            <div className="w-[90%] sm:w-[70%] lg:w-[45%] font-['Charter']">
              <div className={`bg-[#F6F6F6] rounded-[25px] px-4 py-4`}>
                {attachments.length > 0 && (
                  <div className="mb-2 flex flex-wrap gap-2">
                    {attachments.map((attachment, index) => (
                      <div
                        key={index}
                        className="bg-white/80 rounded-lg p-1 pr-2 flex items-center gap-2 max-w-[128px]"
                      >
                        {attachment.type === 'url' ? (
                          <div className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-blue-50 rounded">
                            <LinkOutlined
                              style={{ fontSize: '20px', color: '#1890ff' }}
                            />
                          </div>
                        ) : attachment.type === 'audio' ? (
                          <div
                            className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-red-50 rounded cursor-pointer"
                            onClick={() =>
                              handleAudioPlay(attachment.preview || '')
                            }
                          >
                            {playingAudio === attachment.preview ? (
                              <PauseCircleOutlined
                                style={{ fontSize: '20px', color: '#ff4d4f' }}
                              />
                            ) : (
                              <PlayCircleOutlined
                                style={{ fontSize: '20px', color: '#ff4d4f' }}
                              />
                            )}
                          </div>
                        ) : attachment.preview === 'doc' ? (
                          <div className="h-8 w-8 flex-shrink-0 flex items-center justify-center bg-gray-100 rounded">
                            <FileOutlined style={{ fontSize: '20px' }} />
                          </div>
                        ) : (
                          <img
                            src={attachment.preview || ''}
                            alt="preview"
                            className="h-8 w-8 flex-shrink-0 object-cover rounded"
                          />
                        )}
                        <span className="text-xs text-gray-700 min-w-0 flex-1 truncate">
                          {attachment.name}
                        </span>
                        <button
                          onClick={() => removeAttachment(index)}
                          className="ml-1 flex-shrink-0 text-gray-500 hover:text-gray-700"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                
                <SearchBar
                  border={false}
                  resize={true}
                  handleSearch={handleSearch}
                  inputValue={inputValue}
                  setInputValue={setInputValue}
                  placeholder="write your message here"
                  disable={!inputValue.length}
                  onPaste={(event) =>
                    handlePaste(
                      event as unknown as React.ClipboardEvent<HTMLInputElement>
                    )
                  }
                />
                
                <div className="mb-2 mt-4 flex flex-wrap gap-4 justify-end">
                  <div
                    className="bg-white p-2 px-4 rounded-[10px] cursor-pointer shadow-sm"
                    onClick={() => {
                      const token: any = localStorage.getItem('token');
                      window.open(`https://deep.mergenai.io/chat?token=${encodeURIComponent(token)}`);
                    }}
                  >
                    Deep Research
                  </div>
                  
                  <div
                    className="bg-white p-2 rounded-[10px] cursor-pointer shadow-sm"
                    onClick={handleDocumentClick}
                  >
                    <ShareIcon />
                    <input
                      type="file"
                      ref={documentInputRef}
                      onChange={handleDocumentChange}
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Projects Section - moved inside the main container */}
            {!isLoadingProjects && recentProjects.length > 0 && (
              <div className="w-[90%] sm:w-[70%] lg:w-[45%] mt-4">
                <RecentProjects 
                  projects={recentProjects}
                  onProjectClick={handleOpenProject}
                  onProjectsUpdate={fetchRecentProjects}
                />
              </div>
            )}
          </div>

          <Footer />
        </div>
      </motion.div>

      <audio
        ref={audioRef}
        onEnded={() => setPlayingAudio(null)}
        className="hidden"
      />

      <Modal
        title="Add URL"
        open={isUrlModalOpen}
        onCancel={() => {
          setIsUrlModalOpen(false);
          setUrlInput('');
        }}
        footer={null}
        styles={{
          body: { maxHeight: '70vh' },
        }}
      >
        <div className="m-5">
          <SearchBar
            border={false}
            resize={false}
            handleSearch={handleUrlSubmit}
            setInputValue={setUrlInput}
            inputValue={urlInput}
            placeholder="Enter your Url here..."
            disable={!urlInput.length}
          />
        </div>
      </Modal>
    </>
  );
};

export default Home; 